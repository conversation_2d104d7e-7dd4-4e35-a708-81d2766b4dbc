// Utility functions for authentication and token management
import Cookies from 'js-cookie';

/**
 * Decode JWT token to get expiration time
 */
export const decodeToken = (token: string) => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

/**
 * Check if token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  const decoded = decodeToken(token);
  if (!decoded || !decoded.exp) {
    return true;
  }
  
  const currentTime = Date.now() / 1000;
  return decoded.exp < currentTime;
};

/**
 * Get token from cookies
 */
export const getToken = (): string | null => {
  return Cookies.get('token') || null;
};

/**
 * Set token in cookies
 */
export const setToken = (token: string, rememberMe: boolean = false): void => {
  const cookieOptions = {
    expires: rememberMe ? 1 : 1/24, // 1 day if "remember me", otherwise 1 hour
    secure: false, // true in production with HTTPS
    sameSite: 'lax' as const,
    path: '/'
  };
  Cookies.set('token', token, cookieOptions);
};

/**
 * Get refresh token from cookies
 */
export const getRefreshToken = (): string | null => {
  return Cookies.get('refresh_token') || null;
};

/**
 * Set refresh token in cookies
 */
export const setRefreshToken = (refreshToken: string, rememberMe: boolean = false): void => {
  const cookieOptions = {
    expires: rememberMe ? 30 : 30, // 30 days for refresh token
    secure: false, // true in production with HTTPS
    sameSite: 'lax' as const,
    path: '/'
  };
  Cookies.set('refresh_token', refreshToken, cookieOptions);
};

/**
 * Remove refresh token from cookies
 */
export const removeRefreshToken = (): void => {
  Cookies.remove('refresh_token');
};

/**
 * Remove token from cookies
 */
export const removeToken = (): void => {
  Cookies.remove('token');
};

/**
 * Get user data from cookies
 */
export const getUser = (): any | null => {
  const userData = Cookies.get('user');
  if (!userData) return null;
  try {
    return JSON.parse(userData);
  } catch (error) {
    console.error('Error parsing user data from cookies:', error);
    return null;
  }
};

/**
 * Set user data in cookies
 */
export const setUser = (user: any, rememberMe: boolean = false): void => {
  const cookieOptions = {
    expires: rememberMe ? 1 : 1/24, // 1 day if "remember me", otherwise 1 hour
    secure: false, // true in production with HTTPS
    sameSite: 'lax' as const,
    path: '/'
  };
  Cookies.set('user', JSON.stringify(user), cookieOptions);
};

/**
 * Remove user data from cookies
 */
export const removeUser = (): void => {
  Cookies.remove('user');
};

/**
 * Get role from cookies
 */
export const getRole = (): string | null => {
  return Cookies.get('role') || null;
};

/**
 * Set role in cookies
 */
export const setRole = (role: string, rememberMe: boolean = false): void => {
  const cookieOptions = {
    expires: rememberMe ? 1 : 1/24, // 1 day if "remember me", otherwise 1 hour
    secure: false, // true in production with HTTPS
    sameSite: 'lax' as const,
    path: '/'
  };
  Cookies.set('role', role, cookieOptions);
};

/**
 * Remove role from cookies
 */
export const removeRole = (): void => {
  Cookies.remove('role');
};

/**
 * Clear all authentication data from cookies
 */
export const clearAuthData = (): void => {
  removeToken();
  removeRefreshToken();
  removeUser();
  removeRole();
};

/**
 * Check if user is authenticated and token is valid
 */
export const isAuthenticated = (): boolean => {
  const token = getToken();
  if (!token) {
    return false;
  }
  
  if (isTokenExpired(token)) {
    removeToken();
    return false;
  }
  
  return true;
};

/**
 * Get time remaining until token expires (in seconds)
 */
export const getTokenTimeRemaining = (token: string): number => {
  const decoded = decodeToken(token);
  if (!decoded || !decoded.exp) {
    return 0;
  }

  const currentTime = Date.now() / 1000;
  const timeRemaining = decoded.exp - currentTime;
  return Math.max(0, timeRemaining);
};

/**
 * Refresh access token using refresh token
 */
export const refreshAccessToken = async (): Promise<{ success: boolean; token?: string; user?: any; message?: string }> => {
  try {
    const refreshToken = getRefreshToken();

    if (!refreshToken) {
      return { success: false, message: 'No refresh token available' };
    }

    // Check if refresh token is not expired
    if (isTokenExpired(refreshToken)) {
      clearAuthData();
      return { success: false, message: 'Refresh token expired' };
    }

    const response = await fetch('http://localhost:5000/auth/refresh', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${refreshToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        token: data.token,
        user: data.user,
        message: data.msg
      };
    } else {
      // If refresh token is invalid, clean up data
      if (response.status === 401 || response.status === 422) {
        clearAuthData();
      }
      return { success: false, message: 'Failed to refresh token' };
    }
  } catch (error) {
    console.error('Error refreshing token:', error);
    return { success: false, message: 'Network error during token refresh' };
  }
};

/**
 * Format time remaining in human readable format
 */
export const formatTimeRemaining = (seconds: number): string => {
  if (seconds <= 0) return 'Expired';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m`;
  } else {
    return `${Math.floor(seconds)}s`;
  }
};

/**
 * Debug function to check authentication status
 */
export const debugAuthStatus = (): void => {
  const token = getToken();
  const refreshToken = getRefreshToken();
  const user = getUser();
  const role = getRole();

  console.log('🔍 === DEBUG AUTH STATUS ===');
  console.log('🔑 Access Token:', token ? `${token.substring(0, 50)}...` : 'Not found');
  console.log('🔄 Refresh Token:', refreshToken ? `${refreshToken.substring(0, 50)}...` : 'Not found');
  console.log('👤 User:', user);
  console.log('🎭 Role:', role);

  if (token) {
    const decoded = decodeToken(token);
    const timeRemaining = getTokenTimeRemaining(token);
    const tokenDuration = timeRemaining > 7200 ? '24h (Remember Me)' : '2h (Normal)';

    console.log('⏰ Token expires at:', new Date(decoded.exp * 1000).toLocaleString());
    console.log('⏳ Time remaining:', formatTimeRemaining(timeRemaining));
    console.log('🕐 Token type:', tokenDuration);
    console.log('❓ Is expired:', isTokenExpired(token));
  }

  if (refreshToken) {
    const decodedRefresh = decodeToken(refreshToken);
    console.log('🔄 Refresh token expires at:', new Date(decodedRefresh.exp * 1000).toLocaleString());
    console.log('❓ Refresh token is expired:', isTokenExpired(refreshToken));
    console.log('💡 Refresh logic: Active users without Remember Me only');
  } else {
    console.log('💡 No refresh token: Either Remember Me enabled or not logged in');
  }

  console.log('✅ Is authenticated:', isAuthenticated());
  console.log('🔍 === END DEBUG ===');
};


