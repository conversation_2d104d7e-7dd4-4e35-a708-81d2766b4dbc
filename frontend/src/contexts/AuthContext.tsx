import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { addAlert } from '../store/slices/alertSlice';
import {
  getToken,
  getUser,
  getRole,
  getRefreshToken,
  setToken as saveToken,
  setRefreshToken as saveRefreshToken,
  setUser as saveUser,
  setRole as saveRole,
  clearAuthData,
  isTokenExpired,
  getTokenTimeRemaining,
  refreshAccessToken,
  debugAuthStatus
} from '../utils/auth';

interface User {
  id?: string;
  username?: string;
  email?: string;
  role?: string;
  [key: string]: any;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  role: string | null;
  timeRemaining: number;
  isAuthenticated: boolean;
  loading: boolean;
  login: (token: string, user: User, refreshToken?: string, rememberMe?: boolean) => void;
  logout: () => void;
  tryRefreshToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType);

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuthContext must be used within an AuthProvider');
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [role, setRole] = useState<string | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<number>(7200);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [hasRememberMe, setHasRememberMe] = useState<boolean>(false);
  const [sessionWarningShown, setSessionWarningShown] = useState<boolean>(false);

  useEffect(() => {
    const storedToken = getToken();
    const storedUser = getUser();
    const storedRole = getRole();

    // Ajouter la fonction de debug au window global
    (window as any).debugAuth = debugAuthStatus;

    if (storedToken && storedUser && storedRole) {
      // Vérifier si le token n'est pas expiré
      if (!isTokenExpired(storedToken)) {
        setToken(storedToken);
        setUser(storedUser);
        setRole(storedRole);
        setIsAuthenticated(true);

        // Calculer le temps restant basé sur le token réel
        const remaining = getTokenTimeRemaining(storedToken);
        setTimeRemaining(Math.floor(remaining));
      } else {
        // Token expiré, nettoyer les données
        clearAuthData();
        setIsAuthenticated(false);
      }
    } else if (storedUser && storedRole) {
      // Si on a un utilisateur mais pas de token d'accès, déconnecter automatiquement
      console.log('🔒 No access token found but user data exists, logging out automatically');
      clearAuthData();
      setIsAuthenticated(false);
      navigate('/auth/login');
    } else {
      setIsAuthenticated(false);
    }
    setLoading(false);
  }, [navigate]);

  useEffect(() => {
    const interval = setInterval(async () => {
      const currentToken = getToken();

      if (currentToken && !isTokenExpired(currentToken)) {
        // Mettre à jour le temps restant basé sur le token réel
        const remaining = getTokenTimeRemaining(currentToken);
        setTimeRemaining(Math.floor(remaining));

        // La notification d'expiration est maintenant gérée par SessionExpirationWarning component

        // Si le token expire dans moins de 5 minutes ET que l'utilisateur n'a pas "Remember Me", essayer de le rafraîchir
        if (remaining < 300 && remaining > 0 && !hasRememberMe) {
          console.log('🔄 Token expires soon, attempting refresh...');
          const refreshSuccess = await tryRefreshToken();
          if (!refreshSuccess) {
            console.log('🔒 Failed to refresh token, will logout when expired');
          }
        }
      } else if (currentToken && isTokenExpired(currentToken)) {
        // Token expiré, essayer de le rafraîchir avant de déconnecter SEULEMENT si pas "Remember Me"
        if (!hasRememberMe) {
          console.log('🔄 Token expired, attempting refresh...');
          const refreshSuccess = await tryRefreshToken();
          if (!refreshSuccess) {
            console.log('🔒 Token expired and refresh failed, logging out automatically');
            logout();
          }
        } else {
          console.log('🔒 Token expired with Remember Me, logging out automatically');
          logout();
        }
      } else if (isAuthenticated) {
        // Pas de token mais utilisateur authentifié, déconnecter immédiatement
        console.log('🔒 No access token found but user is authenticated, logging out automatically');
        logout();
      } else {
        // Pas de token, décompte simple
        setTimeRemaining((prev) => (prev > 0 ? prev - 1 : 0));
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [hasRememberMe, isAuthenticated, sessionWarningShown, dispatch]);

  const login = (newToken: string, newUser: User, refreshToken?: string, rememberMe: boolean = false) => {
    setToken(newToken);
    setUser(newUser);
    const userRole = newUser?.role || 'user';
    setRole(userRole);
    setIsAuthenticated(true);
    setHasRememberMe(rememberMe);
    setSessionWarningShown(false); // Reset session warning for new login

    // Calculer le temps restant basé sur le nouveau token
    const remaining = getTokenTimeRemaining(newToken);
    setTimeRemaining(Math.floor(remaining));

    // Sauvegarder dans les cookies au lieu du localStorage
    saveToken(newToken, rememberMe);
    if (refreshToken && !rememberMe) {
      // Sauvegarder le refresh token seulement si pas "Remember Me"
      saveRefreshToken(refreshToken, rememberMe);
      console.log('🔄 Refresh token saved for active session management');
    } else if (rememberMe) {
      console.log('⏰ Remember Me enabled - no refresh token needed (24h session)');
    }
    saveUser(newUser, rememberMe);
    saveRole(userRole, rememberMe);
  };

  const tryRefreshToken = async (): Promise<boolean> => {
    try {
      const refreshResult = await refreshAccessToken();

      if (refreshResult.success && refreshResult.token && refreshResult.user) {
        // Mettre à jour le token et l'utilisateur
        setToken(refreshResult.token);
        setUser(refreshResult.user);
        setRole(refreshResult.user.role);
        setSessionWarningShown(false); // Reset session warning after successful refresh

        // Calculer le nouveau temps restant
        const remaining = getTokenTimeRemaining(refreshResult.token);
        setTimeRemaining(Math.floor(remaining));

        // Sauvegarder le nouveau token
        saveToken(refreshResult.token, false); // Ne pas étendre l'expiration du cookie

        console.log('✅ Token refreshed successfully');
        return true;
      } else {
        console.log('❌ Token refresh failed:', refreshResult.message);
        return false;
      }
    } catch (error) {
      console.error('❌ Error during token refresh:', error);
      return false;
    }
  };

  const logout = () => {
    // Nettoyer toutes les données d'authentification des cookies
    clearAuthData();

    // Nettoyer le localStorage au cas où il y aurait encore des données
    localStorage.clear();

    // Réinitialiser l'état
    setToken(null);
    setUser(null);
    setRole(null);
    setIsAuthenticated(false);

    // Redirect to login page
    navigate('/login');
  };

  return (
    <AuthContext.Provider
      value={{ user, token, role, timeRemaining, isAuthenticated, loading, login, logout, tryRefreshToken }}
    >
      {children}
    </AuthContext.Provider>
  );
};
