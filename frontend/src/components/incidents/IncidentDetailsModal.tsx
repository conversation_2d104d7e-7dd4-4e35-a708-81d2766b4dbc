import { useState, useEffect } from 'react';
import { X, Clock, User, AlertTriangle, XCircle, Edit, Save, Loader } from 'lucide-react';
import { useRole } from '../../hooks/useRole';
import {
  Incident,
  updateIncident,
  getSeverityColor,
  getStatusColor,
  formatDate
} from '../../services/incidentService';
import UserSelector from './UserSelector';

interface IncidentDetailsModalProps {
  incident: Incident | null;
  isOpen: boolean;
  onClose: () => void;
  onIncidentUpdated: () => void;
  initialEditMode?: boolean;
}

export default function IncidentDetailsModal({
  incident,
  isOpen,
  onClose,
  onIncidentUpdated,
  initialEditMode = false
}: IncidentDetailsModalProps) {
  const { isAdmin } = useRole();
  const [isEditing, setIsEditing] = useState(initialEditMode);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editData, setEditData] = useState({
    title: '',
    description: '',
    severity: 'medium' as const,
    status: 'open' as const,
    escalated: false,
    false_positive: false,
    assigned_to: ''
  });

  // Initialize edit data when incident changes
  useEffect(() => {
    if (incident) {
      setEditData({
        title: incident.title,
        description: incident.description,
        severity: incident.severity,
        status: incident.status,
        escalated: incident.escalated || false,
        false_positive: incident.false_positive || false,
        assigned_to: incident.assigned_to || ''
      });
    }
  }, [incident]);

  const handleEdit = () => {
    setIsEditing(true);
    setError(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setError(null);
    // Reset edit data
    setEditData({
      title: incident.title,
      description: incident.description,
      severity: incident.severity,
      status: incident.status,
      escalated: incident.escalated || false,
      false_positive: incident.false_positive || false,
      assigned_to: incident.assigned_to || ''
    });
  };

  const handleSave = async () => {
    setIsSaving(true);
    setError(null);

    try {
      await updateIncident(incident.incident_id, editData);
      setIsEditing(false);
      onIncidentUpdated();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update incident');
    } finally {
      setIsSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-gray-700 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-white">Incident Details</h2>
            <p className="text-gray-400 text-sm">{incident.incident_id}</p>
          </div>
          <div className="flex items-center space-x-2">
            {isAdmin && !isEditing && (
              <button
                onClick={handleEdit}
                className="flex items-center px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
              >
                <Edit size={16} className="mr-1" />
                Edit
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Error Message */}
          {error && (
            <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
              <p className="text-red-200">{error}</p>
            </div>
          )}

          {/* Status and Severity */}
          <div className="flex items-center space-x-4">
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(incident.severity)}`}>
              {incident.severity.toUpperCase()}
            </span>
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(incident.status)}`}>
              {incident.status.replace('_', ' ').toUpperCase()}
            </span>
            {incident.escalated && (
              <span className="flex items-center text-red-400 text-sm">
                <AlertTriangle size={16} className="mr-1" />
                Escalated
              </span>
            )}
            {incident.false_positive && (
              <span className="flex items-center text-yellow-400 text-sm">
                <XCircle size={16} className="mr-1" />
                False Positive
              </span>
            )}
            {incident.assigned_to && (
              <span className="flex items-center text-green-400 text-sm">
                <User size={16} className="mr-1" />
                Assigned to: {incident.assigned_to}
              </span>
            )}
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Title</label>
            {isEditing ? (
              <input
                type="text"
                value={editData.title}
                onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            ) : (
              <p className="text-white text-lg">{incident.title}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
            {isEditing ? (
              <textarea
                value={editData.description}
                onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                rows={4}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            ) : (
              <p className="text-gray-300">{incident.description}</p>
            )}
          </div>

          {/* Edit Fields (when editing) */}
          {isEditing && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Severity</label>
                <select
                  value={editData.severity}
                  onChange={(e) => setEditData(prev => ({ ...prev, severity: e.target.value as any }))}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Status</label>
                <select
                  value={editData.status}
                  onChange={(e) => setEditData(prev => ({ ...prev, status: e.target.value as any }))}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="open">Open</option>
                  <option value="in_progress">In Progress</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>
              </div>
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">Assigned To</label>
                <UserSelector
                  onUserSelect={(user) => setEditData(prev => ({ ...prev, assigned_to: user.email }))}
                  placeholder="Select user to assign incident"
                  showEmailOnly={true}
                />
              </div>
              <div className="col-span-2 space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={editData.escalated}
                    onChange={(e) => setEditData(prev => ({ ...prev, escalated: e.target.checked }))}
                    className="mr-2 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="text-white">Escalated</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={editData.false_positive}
                    onChange={(e) => setEditData(prev => ({ ...prev, false_positive: e.target.checked }))}
                    className="mr-2 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="text-white">False Positive</span>
                </label>
              </div>
            </div>
          )}

          {/* Note: File attachments are intentionally not available for incidents.
              Attachments are only supported for investigation tickets where evidence collection occurs.
              Incidents focus on high-level status and assignment management. */}

          {/* Metadata */}
          <div className="grid grid-cols-2 gap-4 text-sm text-gray-400">
            <div>
              <span className="flex items-center">
                <Clock size={16} className="mr-1" />
                Created: {formatDate(incident.created_at)}
              </span>
            </div>
            <div>
              <span className="flex items-center">
                <Clock size={16} className="mr-1" />
                Updated: {formatDate(incident.updated_at)}
              </span>
            </div>
          </div>

          {/* Action Buttons (when editing) */}
          {isEditing && (
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-700">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-gray-300 hover:text-white transition-colors"
                disabled={isSaving}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
              >
                {isSaving ? (
                  <>
                    <Loader className="animate-spin mr-2" size={16} />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save size={16} className="mr-2" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
